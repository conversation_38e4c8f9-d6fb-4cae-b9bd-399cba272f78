import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/shared/lib/css'

const badgeVariants = cva(
    'inline-flex items-center justify-center rounded-[100px] border text-sm font-medium w-fit whitespace-nowrap shrink-0 gap-1.5 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',
    {
        variants: {
            variant: {
                default:
                    'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',
                secondary:
                    'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',
                accent: 'border-transparent bg-badge-green text-white [a&]:hover:bg-badge-green/90',
                accentClear:
                    'border-transparent bg-badge-green/10 text-badge-green [a&]:hover:bg-badge-green/20',
                warning:
                    'border-transparent bg-badge-orange text-white [a&]:hover:bg-badge-orange/90',
                warningClear:
                    'border-transparent bg-badge-orange/10 text-badge-orange [a&]:hover:bg-badge-orange/20',
                destructive:
                    'border-transparent bg-badge-red text-white [a&]:hover:bg-badge-red/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
                destructiveClear:
                    'border-transparent bg-badge-red/10 text-badge-red [a&]:hover:bg-badge-red/20',
                outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
                outlineR:
                    'text-foreground border-raciq-blue border-2 [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
                outlineA:
                    'text-foreground border-raciq-green border-2 [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
                outlineC:
                    'text-foreground border-raciq-orange border-2 [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
                outlineI:
                    'text-foreground border-raciq-grey border-2 [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
                outlineQ:
                    'text-foreground border-raciq-red border-2 [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
            },
            size: {
                default: 'h-7.5 px-3.75 [&>svg]:size-4',
                sm: 'h-6 px-3 text-xs [&>svg]:size-3',
                lg: 'h-8.5 px-5 text-sm gap-2.5 [&>svg]:size-5',
                raciq: 'size-6.5 text-base rounded-full',
            },
        },
        defaultVariants: {
            variant: 'default',
        },
    },
)

function Badge({
    className,
    variant,
    size,
    asChild = false,
    ...props
}: React.ComponentProps<'span'> &
    VariantProps<typeof badgeVariants> & {
        asChild?: boolean
    }) {
    const Comp = asChild ? Slot : 'span'

    return (
        <Comp
            data-slot="badge"
            className={cn(badgeVariants({ variant, size }), className)}
            {...props}
        />
    )
}

export { Badge, badgeVariants }
