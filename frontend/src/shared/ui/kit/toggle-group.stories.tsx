import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite'

import { Bold, Italic, Underline } from 'lucide-react'

import { ToggleGroup, ToggleGroupItem } from './toggle-group'

const meta = {
    component: ToggleGroup,
} satisfies Meta<typeof ToggleGroup>

export default meta

type Story = StoryObj<typeof meta>

export const DefaultSingle: Story = {
    render: (args) => (
        <ToggleGroup {...args}>
            <ToggleGroupItem value="weeks">Недели</ToggleGroupItem>
            <ToggleGroupItem value="months">Месяцы</ToggleGroupItem>
        </ToggleGroup>
    ),
    args: {
        defaultValue: 'weeks',
        size: 'default',
        type: 'single',
        variant: 'default',
    },
}

export const DefaultMultiple: Story = {
    args: {
        children: (
            <>
                <ToggleGroupItem
                    value="bold"
                    aria-label="Toggle bold"
                >
                    <Bold className="h-4 w-4" />
                </ToggleGroupItem>
                <ToggleGroupItem
                    value="italic"
                    aria-label="Toggle italic"
                >
                    <Italic className="h-4 w-4" />
                </ToggleGroupItem>
                <ToggleGroupItem
                    value="strikethrough"
                    aria-label="Toggle strikethrough"
                >
                    <Underline className="h-4 w-4" />
                </ToggleGroupItem>
            </>
        ),
        defaultValue: ['bold'],
        size: 'default',
        type: 'multiple',
        variant: 'default',
    },
}

export const Outline: Story = {
    args: {
        children: [
            <ToggleGroupItem
                key="weeks"
                value="weeks"
            >
                Недели
            </ToggleGroupItem>,
            <ToggleGroupItem
                key="months"
                value="months"
            >
                Месяцы
            </ToggleGroupItem>,
        ],
        defaultValue: 'weeks',
        size: 'default',
        type: 'single',
        variant: 'outline',
    },
}

export const Small: Story = {
    args: {
        children: [
            <ToggleGroupItem
                key="weeks"
                value="weeks"
            >
                Недели
            </ToggleGroupItem>,
            <ToggleGroupItem
                key="months"
                value="months"
            >
                Месяцы
            </ToggleGroupItem>,
        ],
        defaultValue: 'weeks',
        size: 'sm',
        type: 'single',
        variant: 'default',
    },
}

export const Large: Story = {
    args: {
        children: [
            <ToggleGroupItem
                key="weeks"
                value="weeks"
            >
                Недели
            </ToggleGroupItem>,
            <ToggleGroupItem
                key="months"
                value="months"
            >
                Месяцы
            </ToggleGroupItem>,
        ],
        defaultValue: 'weeks',
        size: 'lg',
        type: 'single',
        variant: 'default',
    },
}
