import type { Meta, StoryObj } from '@storybook/react-vite'

import { Bold } from 'lucide-react'

import { Toggle } from './toggle'

const meta = {
    component: Toggle,
} satisfies Meta<typeof Toggle>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: <Bold className="h-4 w-4" />,
        size: 'default',
        variant: 'default',
    },
}

export const Outline: Story = {
    args: {
        children: <Bold className="h-4 w-4" />,
        size: 'default',
        variant: 'outline',
    },
}

export const Small: Story = {
    args: {
        children: <Bold className="h-4 w-4" />,
        size: 'sm',
        variant: 'default',
    },
}

export const Large: Story = {
    args: {
        children: <Bold className="h-4 w-4" />,
        size: 'lg',
        variant: 'default',
    },
}
