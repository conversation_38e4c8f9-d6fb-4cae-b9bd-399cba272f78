/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as PageLayoutRouteImport } from './routes/_page-layout'
import { Route as PageLayoutIndexRouteImport } from './routes/_page-layout/index'
import { Route as PageLayoutTestIndexRouteImport } from './routes/_page-layout/test/index'
import { Route as PageLayoutTestIdRouteImport } from './routes/_page-layout/test/$id'

const PageLayoutRoute = PageLayoutRouteImport.update({
  id: '/_page-layout',
  getParentRoute: () => rootRouteImport,
} as any)
const PageLayoutIndexRoute = PageLayoutIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => PageLayoutRoute,
} as any)
const PageLayoutTestIndexRoute = PageLayoutTestIndexRouteImport.update({
  id: '/test/',
  path: '/test/',
  getParentRoute: () => PageLayoutRoute,
} as any)
const PageLayoutTestIdRoute = PageLayoutTestIdRouteImport.update({
  id: '/test/$id',
  path: '/test/$id',
  getParentRoute: () => PageLayoutRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof PageLayoutIndexRoute
  '/test/$id': typeof PageLayoutTestIdRoute
  '/test': typeof PageLayoutTestIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof PageLayoutIndexRoute
  '/test/$id': typeof PageLayoutTestIdRoute
  '/test': typeof PageLayoutTestIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_page-layout': typeof PageLayoutRouteWithChildren
  '/_page-layout/': typeof PageLayoutIndexRoute
  '/_page-layout/test/$id': typeof PageLayoutTestIdRoute
  '/_page-layout/test/': typeof PageLayoutTestIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/test/$id' | '/test'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/test/$id' | '/test'
  id:
    | '__root__'
    | '/_page-layout'
    | '/_page-layout/'
    | '/_page-layout/test/$id'
    | '/_page-layout/test/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  PageLayoutRoute: typeof PageLayoutRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_page-layout': {
      id: '/_page-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PageLayoutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_page-layout/': {
      id: '/_page-layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof PageLayoutIndexRouteImport
      parentRoute: typeof PageLayoutRoute
    }
    '/_page-layout/test/': {
      id: '/_page-layout/test/'
      path: '/test'
      fullPath: '/test'
      preLoaderRoute: typeof PageLayoutTestIndexRouteImport
      parentRoute: typeof PageLayoutRoute
    }
    '/_page-layout/test/$id': {
      id: '/_page-layout/test/$id'
      path: '/test/$id'
      fullPath: '/test/$id'
      preLoaderRoute: typeof PageLayoutTestIdRouteImport
      parentRoute: typeof PageLayoutRoute
    }
  }
}

interface PageLayoutRouteChildren {
  PageLayoutIndexRoute: typeof PageLayoutIndexRoute
  PageLayoutTestIdRoute: typeof PageLayoutTestIdRoute
  PageLayoutTestIndexRoute: typeof PageLayoutTestIndexRoute
}

const PageLayoutRouteChildren: PageLayoutRouteChildren = {
  PageLayoutIndexRoute: PageLayoutIndexRoute,
  PageLayoutTestIdRoute: PageLayoutTestIdRoute,
  PageLayoutTestIndexRoute: PageLayoutTestIndexRoute,
}

const PageLayoutRouteWithChildren = PageLayoutRoute._addFileChildren(
  PageLayoutRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  PageLayoutRoute: PageLayoutRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
